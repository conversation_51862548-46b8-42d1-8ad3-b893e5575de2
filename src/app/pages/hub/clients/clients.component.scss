//https://stackblitz.com/edit/ngx-signature-pad-mawfqk?file=src%2Fapp%2Fapp.component.ts

// Draft contracts section
.draft-contracts-list {
  .draft-contract-item {
    background: #fff3cd;
    border-color: #ffeaa7 !important;
    transition: all 0.3s ease;

    &:hover {
      background: #fff1b8;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .text-muted {
      color: #6c757d !important;
    }

    strong {
      color: #856404;
    }
  }
}

// Table improvements
::ng-deep {
  .p-datatable {
    .p-datatable-header {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .p-datatable-tbody > tr {
      transition: background-color 0.3s ease;

      &:hover {
        background: #f8f9fa !important;
      }
    }
  }
}

// Button improvements
.p-button-success {
  background: #28a745;
  border-color: #28a745;

  &:hover {
    background: #218838;
    border-color: #1e7e34;
  }
}

// Warning elements
.text-warning {
  color: #856404 !important;
  font-weight: 600;

  i {
    margin-right: 0.5rem;
  }
}
