<div class="main-container d-flex" [ngClass]="{'light': themeService.mode === 'light', 'dark-blue': themeService.mode === 'dark'}">
    <div class="sidebar shadow-sm">
        <app-hub-sidebar></app-hub-sidebar>
    </div>
    <div class="main-content">
        <div class="hub-header">
            <app-hub-header></app-hub-header>
        </div>
        <div class="hub-outlet">
            <router-outlet></router-outlet>
        </div>
    </div>
</div>