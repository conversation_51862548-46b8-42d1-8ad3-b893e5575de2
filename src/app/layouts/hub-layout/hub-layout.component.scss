.main-container {
    width: 100vw;
    height: 100vh;
    background: var(--main-bg);
    overflow: hidden;
    .sidebar {
        width: 250px;
        height: calc(100vh - 14px);
        overflow-y: auto;
        margin: 7px;
        background: var(--secondary-bg);
        background: hsla(0,0%,100%,.8)!important;
        backdrop-filter: saturate(200%) blur(30px);
        border-radius: 8px;
    }
    .main-content {
        max-width: calc(100vw - 264px);
        width: 100%;
        min-height: 100vh;
        padding: 7px;
        background: var(--main-bg);
        overflow-y: auto;
        position: relative;
        .hub-header {
            top: 7px;
            position: sticky;
            margin: 16px 0;
            z-index: 1042;
        }
        .hub-outlet {
            width: 100%;
        }
    }
}