<!-- tenant-management.component.html -->
<div class="flex justify-content-between align-items-center mb-4">
  <h5>Tenant Management</h5>
  <div class="d-flex gap-2 justify-content-between">
    <span class="p-input-icon-left">
      <i class="pi pi-search"></i>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchText"
        (input)="filterTenants()"
        placeholder="Search tenants..."
      />
    </span>
    <button
      pButton
      icon="pi pi-plus"
      label="New Tenant Contract"
      (click)="openNewTenantForm()"
      class="p-button-primary"
    ></button>
    <button
      pButton
      icon="pi pi-plus"
      label="Legacy Form"
      (click)="openNew()"
      class="p-button-secondary"
    ></button>
  </div>
</div>

<!-- Draft Contracts Section -->
<div class="mb-4" *ngIf="draftContracts.length > 0">
  <h6>Draft Contracts</h6>
  <p-table
    [value]="draftContracts"
    styleClass="p-datatable-sm"
    [paginator]="true"
    [rows]="5"
  >
    <ng-template pTemplate="header">
      <tr>
        <th>Contract Number</th>
        <th>Tenant Name</th>
        <th>Office</th>
        <th>Current Step</th>
        <th>Last Updated</th>
        <th>Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-draft>
      <tr>
        <td>{{ draft.contractNumber }}</td>
        <td>{{ draft.tenant?.basicInfo?.fullName || "Not specified" }}</td>
        <td>{{ draft.officeId || "Not selected" }}</td>
        <td>{{ draft.currentStep || 0 }}/4</td>
        <td>{{ draft.updatedAt | date : "short" }}</td>
        <td>
          <button
            pButton
            icon="pi pi-pencil"
            class="p-button-rounded p-button-text p-button-sm"
            (click)="openEditDraftContract(draft.id!)"
            pTooltip="Edit Draft"
          ></button>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-table
  [value]="filteredTenants"
  [loading]="loading"
  [paginator]="true"
  [rows]="10"
  styleClass="p-datatable-sm"
>
  <ng-template pTemplate="header">
    <tr>
      <th>Name</th>
      <th>Company</th>
      <th>Email</th>
      <th>Phone</th>
      <th>Actions</th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-tenant>
    <tr>
      <td>{{ tenant.basicInfo.fullName }}</td>
      <td>{{ tenant.basicInfo.companyName || "-" }}</td>
      <td>{{ tenant.basicInfo.email }}</td>
      <td>{{ tenant.basicInfo.phone }}</td>
      <td>
        <button
          pButton
          icon="pi pi-pencil"
          class="p-button-rounded p-button-text"
          (click)="openEdit(tenant)"
        ></button>
      </td>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="5">No tenants found</td>
    </tr>
  </ng-template>
</p-table>

<!-- Multi-step Dialog -->
<p-dialog
  [(visible)]="displayDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [closable]="true"
  [dismissableMask]="true"
  [style]="{
    width: '100vw',
    height: '100vh',
    maxWidth: '100vw',
    maxHeight: '100vh',
    top: '0',
    left: '0'
  }"
  contentStyle="height: calc(100vh - 4rem); overflow: auto;"
  [baseZIndex]="10000"
  [header]="(selectedTenant ? 'Edit' : 'Create') + ' Tenant'"
>
  <p-steps [model]="steps" [activeIndex]="currentStep"></p-steps>

  <div class="form-container mt-4">
    <!-- Step 1: Basic Info -->
    <form *ngIf="currentStep === 0" [formGroup]="tenantForm">
      <div class="p-fluid grid">
        <div class="field col-12 md:col-6">
          <label for="fullName">Full Name*</label>
          <input
            id="fullName"
            pInputText
            formControlName="fullName"
            [class]="{
              'ng-invalid ng-dirty':
                tenantForm.get('basicInfo.fullName')?.invalid &&
                tenantForm.get('basicInfo.fullName')?.dirty
            }"
          />
          <small
            *ngIf="
              tenantForm.get('basicInfo.fullName')?.invalid &&
              tenantForm.get('basicInfo.fullName')?.dirty
            "
            class="p-error"
          >
            Full name is required
          </small>
        </div>
        <!-- Add other basic info fields here -->
      </div>
    </form>

    <!-- Step 2: Lease Details -->
    <form *ngIf="currentStep === 1" [formGroup]="tenantForm">
      <div class="p-fluid grid">
        <div class="field col-12 md:col-6">
          <label for="buildingId">Building*</label>
          <p-dropdown
            id="buildingId"
            formControlName="buildingId"
            [options]="buildings"
            optionLabel="name"
            placeholder="Select Building"
          ></p-dropdown>
        </div>
        <!-- Add other lease detail fields here -->
      </div>
    </form>

    <!-- Step 3: Additional Services -->
    <form *ngIf="currentStep === 2" [formGroup]="tenantForm">
      <div class="p-fluid grid">
        <div class="field col-12">
          <p-checkbox
            formControlName="internet"
            label="Internet Package"
            [binary]="true"
          ></p-checkbox>
        </div>
        <!-- Add other service checkboxes here -->
      </div>
    </form>

    <!-- Step 4: Review -->
    <div *ngIf="currentStep === 3">
      <div class="review-section">
        <h4>Basic Information</h4>
        <p>
          <strong>Name:</strong>
          {{ tenantForm.get("basicInfo.fullName")?.value }}
        </p>
        <!-- Add other review fields here -->
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-between">
      <button
        pButton
        label="Back"
        icon="pi pi-arrow-left"
        *ngIf="currentStep > 0"
        (click)="prevStep()"
        class="p-button-text"
      ></button>
      <div></div>
      <!-- Spacer -->
      <div>
        <button
          pButton
          label="Cancel"
          icon="pi pi-times"
          (click)="displayDialog = false"
          class="p-button-text"
        ></button>
        <button
          pButton
          [label]="currentStep === steps.length - 1 ? 'Save' : 'Next'"
          [icon]="
            currentStep === steps.length - 1
              ? 'pi pi-check'
              : 'pi pi-arrow-right'
          "
          (click)="currentStep === steps.length - 1 ? saveTenant() : nextStep()"
          class="ml-2"
        ></button>
      </div>
    </div>
  </ng-template>
</p-dialog>

<!-- New Multi-step Tenant Form -->
<app-multi-step-tenant-form
  [(visible)]="showTenantForm"
  [contractId]="selectedContractId"
  (contractCreated)="onContractCreated($event)"
  (contractFinalized)="onContractFinalized($event)"
></app-multi-step-tenant-form>
