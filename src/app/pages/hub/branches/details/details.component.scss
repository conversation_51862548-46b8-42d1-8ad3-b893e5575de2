.no-offices {
  min-height: 240px;
  display: flex;
  vertical-align: middle;
  align-items: center;
  justify-content: center;

  .bi {
    font-size: 64px;
  }
}

:host ::ng-deep {
  .normal-sidebar .p-sidebar {
    width: 800px !important;
  }
}

.offices-cards {
  display: flex;
  flex-wrap: wrap;

  .office-card {
    background: transparent;
    width: 100px;
    height: 72px;
    display: flex;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    h6 {
      font-size: 24px;
    }
  }

  .available-card {
    border-color: #3fb950 !important;
    background: rgba(63, 185, 80, 0.1);
    color: #3fb950;
    &:hover {
      background: #3fb950;
      color: #fff;
    }
  }
  .occupied-card {
    border-color: red !important;
    background: rgba(255, 0, 0, 0.1);
    color: #ff0000;
    &:hover {
      background: red;
      color: #fff;
    }
  }
}

.field {
    display: flex;
    flex-direction: column;
}

.row .field {
    width: calc(50% - 7px);
}