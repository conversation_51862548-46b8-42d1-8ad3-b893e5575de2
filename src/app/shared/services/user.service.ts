import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  currentUser: any;
  userRole: string = ''; //<PERSON><PERSON><PERSON><PERSON>, owner, manager, assistant, client, technician, teaBoy, cleaner,

  constructor(private afAuth: AngularFireAuth, private afs: AngularFirestore) {}

  getCurrentUser(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(
        (user) => {
          if (user) {
            this.afs
              .collection('users')
              .doc(user.uid)
              .valueChanges()
              .subscribe(
                (res) => {
                  this.currentUser = res;
                  this.userRole = this.currentUser.role;
                  resolve(this.currentUser);
                },
                (error) => reject(error)
              );
          } else {
            resolve(null);
          }
        },
        (error) => reject(error)
      );
    });
  }
}
