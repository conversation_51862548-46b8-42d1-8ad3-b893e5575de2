import { Component, Input, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';

@Component({
  selector: 'app-details',
  templateUrl: './details.component.html',
  styleUrls: ['./details.component.scss'],
})
export class DetailsComponent implements OnInit {
  @Input() branch: any;
  @Input() isRegularUser: boolean = false;

  offices: any[] = [];
  addOneOffice: boolean = false;
  addBulkOffice: boolean = false;
  openOfficeDetails: boolean = false;

  selectedOffice: any;

  officeNumber!: number;
  startNumber!: number;
  lastNumber!: number;

  constructor(private afs: AngularFirestore, private afAuth: AngularFireAuth) {}

  ngOnInit() {
    this.getOffices();
  }

  getOffices() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs
          .collection('offices', (ref) =>
            ref.where('branchId', '==', this.branch.docid)
          )
          .valueChanges()
          .subscribe((offices: any) => {
            this.offices = offices.sort((a: any, b: any) => a.officeNumber - b.officeNumber);
          });
      }
    })
  }

  showOfficeDetails(office: any) {
    this.selectedOffice = office;
    this.openOfficeDetails = true;
  }

  saveOneOffice() {
    console.log('startNumber', this.branch.docid);
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        const docId = this.afs.createId();
        this.afs
          .collection('offices')
          .doc(docId)
          .set({
            branchId: this.branch.docid,
            officeId: docId,
            officeNumber: this.officeNumber,
            status: 'available',
            companyId: '',
            companyName: '',
            ressources: [],
            startRentDate: new Date(),
            endRentDate: new Date(),
            createdAt: new Date(),
          })
          .then(() => {
            console.log('Office added successfully');
          });
      }
    });
  }

  saveBulkOffice() {
    console.log('startNumber', this.branch.docid);
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        for (let index = this.startNumber; index < this.lastNumber+1; index++) {
          const docId = this.afs.createId();
          this.afs
            .collection('offices')
            .doc(docId)
            .set({
              branchId: this.branch.docid,
              officeId: docId,
              officeNumber: index,
              status: 'available',
              companyId: '',
              companyName: '',
              ressources: [],
              startRentDate: new Date(),
              endRentDate: new Date(),
              createdAt: new Date(),
            })
            .then(() => {
              console.log('Office added successfully');
            });
        }
      }
    });
  }
}
