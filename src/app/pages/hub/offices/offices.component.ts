import { Component, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import * as echarts from 'echarts';
import { OwnerService } from 'src/app/shared/services/owner.service';

@Component({
  selector: 'app-offices',
  templateUrl: './offices.component.html',
  styleUrls: ['./offices.component.scss'],
})
export class OfficesComponent implements OnInit, OnChanges {
  userId$ = this.ownerService.userId$;

  resourceForm: FormGroup;
  resources: any[] = [];
  branches: any[] = [];
  selectedBranch: any;
  branchSectors: any[] = [];
  sect: any;
  offices: any[] = [];

  sectorName: string = '';

  constructor(
    private afAuth: AngularFireAuth,
    private afs: AngularFirestore,
    private ownerService: OwnerService
  ) {
    this.resourceForm = new FormGroup({
      image: new FormControl(''),
      name: new FormControl('', Validators.required),
      color: new FormControl(''),
    });
  }

  ngOnInit() {
    this.userId$.subscribe((id) => {
      console.log('User ID:', id); // Always reflects current value
    });

    //var myChart = echarts.init(document.getElementById('main'));
    this.getResources();
    this.getBranches();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes) {
      this.userId$.subscribe((id) => {
        console.log('User ID:', id); // Always reflects current value
      });
    }
  }

  getResources() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs
          .collection('OfficeResources')
          .doc(user.uid)
          .collection('resources')
          .valueChanges()
          .subscribe((res) => {
            this.resources = res;
            console.log(this.resources);
          });
      }
    });
  }

  getBranches() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs
          .collection('branches', (ref) => ref.where('uid', '==', user.uid))
          .valueChanges()
          .subscribe((res) => {
            this.branches = res;
          });
      }
    });
  }

  addSector() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        const sectors = this.selectedBranch.sectors;
        sectors.push({
          name: this.sectorName,
          //officeNumbers: 0
        });
        this.afs
          .collection('branches')
          .doc(user.uid)
          .collection('branches')
          .doc(this.selectedBranch.id)
          .update({
            sectors: sectors,
          })
          .then(() => {
            console.log('OOOKKKKKK');
          });
      }
    });
  }

  getSectors() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs
          .collection('branches')
          .doc(user.uid)
          .collection('branches')
          .doc(this.selectedBranch.id)
          .valueChanges()
          .subscribe((res) => {
            this.sect = res;
            this.branchSectors = this.sect.sectors;
          });
      }
    });
  }

  addResourceType() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        const resourceId = this.afs.createId();
        this.afs
          .collection('OfficeResources')
          .doc(user.uid)
          .collection('resources')
          .doc(resourceId)
          .set({
            id: resourceId,
            name: this.resourceForm.value.name,
            color: this.resourceForm.value.color,
            image: this.resourceForm.value.image,
          })
          .then(() => {
            console.log('Resource added successfully');
          });
      }
    });
  }
}
