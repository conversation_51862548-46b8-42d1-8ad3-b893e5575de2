import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { TenantFormService } from './tenant-form.service';
import { ContractService } from './contract.service';
import { FormGroup } from '@angular/forms';
import { Tenant, Contract } from './tenant.model';
import { MessageService } from 'primeng/api';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-clients',
  templateUrl: './clients.component.html',
  styleUrls: ['./clients.component.scss'],
  providers: [MessageService],
})
export class ClientsComponent implements OnInit, OnDestroy {
  tenants: Tenant[] = [];
  contracts: Contract[] = [];
  draftContracts: Contract[] = [];
  buildings: any[] = [];
  filteredTenants: Tenant[] = [];
  searchText: string = '';

  // Multi-step form dialog
  showTenantForm: boolean = false;
  selectedContractId?: string;

  // Legacy form (keeping for backward compatibility)
  displayDialog: boolean = false;
  currentStep: number = 0;
  tenantForm: FormGroup;
  selectedTenant: Tenant | null = null;
  loading: boolean = false;

  private destroy$ = new Subject<void>();

  steps = [
    { label: 'Basic Info', icon: 'pi pi-user' },
    { label: 'Lease Details', icon: 'pi pi-file' },
    { label: 'Additional Services', icon: 'pi pi-cog' },
    { label: 'Review', icon: 'pi pi-check' },
  ];

  constructor(
    private tenantFormService: TenantFormService,
    private contractService: ContractService,
    private messageService: MessageService
  ) {
    this.tenantForm = this.tenantFormService.createTenantForm();
  }

  ngOnInit(): void {
    this.loadTenants();
    this.loadContracts();
    this.loadDraftContracts();
    this.loadBuildings();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadTenants(): void {
    this.loading = true;
    // Simulate API call - in real implementation, load from Firebase
    setTimeout(() => {
      this.tenants = [];
      this.filteredTenants = [...this.tenants];
      this.loading = false;
    }, 1000);
  }

  loadContracts(): void {
    this.contractService
      .getAllContracts()
      .pipe(takeUntil(this.destroy$))
      .subscribe((contracts) => {
        this.contracts = contracts;
      });
  }

  loadDraftContracts(): void {
    this.contractService
      .getDraftContracts()
      .pipe(takeUntil(this.destroy$))
      .subscribe((drafts) => {
        this.draftContracts = drafts;
      });
  }

  loadBuildings(): void {
    // Initialize buildings array with sample data or load from service
    this.buildings = [
      { id: '1', name: 'Building A', address: '123 Main St' },
      { id: '2', name: 'Building B', address: '456 Oak Ave' },
      { id: '3', name: 'Building C', address: '789 Pine Rd' },
    ];
  }

  filterTenants(): void {
    if (!this.searchText) {
      this.filteredTenants = [...this.tenants];
      return;
    }

    this.filteredTenants = this.tenants.filter(
      (tenant) =>
        tenant.basicInfo.fullName
          .toLowerCase()
          .includes(this.searchText.toLowerCase()) ||
        tenant.basicInfo.email
          .toLowerCase()
          .includes(this.searchText.toLowerCase()) ||
        tenant.basicInfo.companyName
          ?.toLowerCase()
          .includes(this.searchText.toLowerCase())
    );
  }

  // New multi-step form methods
  openNewTenantForm(): void {
    this.selectedContractId = undefined;
    this.showTenantForm = true;
  }

  openEditDraftContract(contractId: string): void {
    console.log('Opening edit contract with ID:', contractId); // Debug log
    this.selectedContractId = contractId;
    this.showTenantForm = true;
  }

  // Alias method for backward compatibility
  openEditContract(contractId: string): void {
    this.openEditDraftContract(contractId);
  }

  onContractCreated(contractId: string): void {
    console.log('Contract created:', contractId);
    this.loadDraftContracts();
    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Draft contract created successfully',
    });
  }

  onContractFinalized(contractId: string): void {
    console.log('Contract finalized:', contractId);
    this.loadContracts();
    this.loadDraftContracts();
    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Contract finalized successfully',
    });
  }

  // Legacy methods (keeping for backward compatibility)
  openNew(): void {
    this.selectedTenant = null;
    this.tenantForm = this.tenantFormService.createTenantForm();
    this.currentStep = 0;
    this.displayDialog = true;
  }

  openEdit(tenant: Tenant): void {
    this.selectedTenant = tenant;
    this.tenantForm = this.tenantFormService.createTenantForm(tenant);
    this.currentStep = 0;
    this.displayDialog = true;
  }

  nextStep(): void {
    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++;
    }
  }

  prevStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  saveTenant(): void {
    if (this.tenantForm.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill all required fields',
      });
      return;
    }

    const tenantData = this.tenantForm.value;
    if (this.selectedTenant) {
      // Update existing tenant
      const index = this.tenants.findIndex(
        (t) => t.id === this.selectedTenant?.id
      );
      if (index !== -1) {
        this.tenants[index] = { ...tenantData, id: this.selectedTenant.id };
      }
    } else {
      // Add new tenant
      tenantData.id = Math.random().toString(36).substring(2);
      this.tenants.unshift(tenantData);
    }

    this.filteredTenants = [...this.tenants];
    this.displayDialog = false;
    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: `Tenant ${
        this.selectedTenant ? 'updated' : 'created'
      } successfully`,
    });
  }
}
