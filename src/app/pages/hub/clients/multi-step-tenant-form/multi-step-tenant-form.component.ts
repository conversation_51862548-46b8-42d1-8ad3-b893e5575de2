import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ChangeDetectorRef,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable, Subject, takeUntil } from 'rxjs';
import { MessageService } from 'primeng/api';

import { TenantFormService } from '../tenant-form.service';
import { ContractService } from '../contract.service';
import { Contract, Office } from '../tenant.model';

@Component({
  selector: 'app-multi-step-tenant-form',
  templateUrl: './multi-step-tenant-form.component.html',
  styleUrls: ['./multi-step-tenant-form.component.scss'],
})
export class MultiStepTenantFormComponent implements OnInit, OnDestroy {
  @Input() visible: boolean = false;
  @Input() selectedOffice?: Office; // Pre-selected office (from offices module)
  @Input() contractId?: string; // For editing existing draft
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() contractCreated = new EventEmitter<string>();
  @Output() contractFinalized = new EventEmitter<string>();

  form: FormGroup;
  currentStep: number = 0;
  loading: boolean = false;
  saving: boolean = false;

  currentContract?: Contract;
  availableOffices$: Observable<Office[]>;
  branches$: Observable<any[]>;
  selectedBranchId: string = '';

  private destroy$ = new Subject<void>();

  steps = [
    { label: 'Basic Info', icon: 'pi pi-user', key: 'basic' },
    { label: 'Select Office', icon: 'pi pi-building', key: 'office' },
    { label: 'Lease Details', icon: 'pi pi-file', key: 'lease' },
    { label: 'Additional Services', icon: 'pi pi-cog', key: 'services' },
    { label: 'Review & Confirm', icon: 'pi pi-check', key: 'review' },
  ];

  constructor(
    private tenantFormService: TenantFormService,
    private contractService: ContractService,
    private messageService: MessageService,
    private cdr: ChangeDetectorRef
  ) {
    this.form = this.tenantFormService.createTenantContractForm();
    this.branches$ = this.contractService.getUserBranches();
    this.availableOffices$ = this.contractService.getAvailableOffices();
  }

  ngOnInit(): void {
    if (this.contractId) {
      this.loadExistingContract();
    } else if (this.selectedOffice) {
      this.initializeWithOffice();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadExistingContract(): void {
    if (!this.contractId) return;

    this.loading = true;
    console.log('Loading contract with ID:', this.contractId);

    // Use the contract service to get the draft
    this.contractService
      .getContract(this.contractId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (contract) => {
          console.log('Contract service returned:', contract);
          if (contract) {
            this.loadContractData(contract);
          } else {
            console.log(
              'Contract service returned null, trying direct localStorage access'
            );
            this.tryDirectLocalStorageAccess();
          }
        },
        error: (error) => {
          console.error('Error from contract service:', error);
          this.tryDirectLocalStorageAccess();
        },
      });
  }

  private tryDirectLocalStorageAccess(): void {
    try {
      const rawData = localStorage.getItem('draft_contracts');
      console.log('Direct localStorage access - raw data:', rawData);

      if (rawData) {
        const contracts = JSON.parse(rawData);
        const foundContract = contracts.find(
          (c: any) => c.id === this.contractId
        );
        console.log(
          'Direct localStorage access - found contract:',
          foundContract
        );

        if (foundContract) {
          this.loadContractData(foundContract);
          return;
        }
      }
    } catch (error) {
      console.error('Direct localStorage access failed:', error);
    }

    // If we get here, no contract was found
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load contract draft',
    });
    this.loading = false;
  }

  private loadContractData(contract: any): void {
    console.log('Loading contract data:', contract);

    // Convert date strings to Date objects if needed
    if (typeof contract.createdAt === 'string') {
      contract.createdAt = new Date(contract.createdAt);
    }
    if (typeof contract.updatedAt === 'string') {
      contract.updatedAt = new Date(contract.updatedAt);
    }
    if (contract.startDate && typeof contract.startDate === 'string') {
      contract.startDate = new Date(contract.startDate);
    }
    if (contract.endDate && typeof contract.endDate === 'string') {
      contract.endDate = new Date(contract.endDate);
    }

    this.currentContract = contract;
    this.currentStep = contract.currentStep || 0;

    console.log('Creating form with contract:', contract);

    // Create new form with contract data
    const newForm = this.tenantFormService.createTenantContractForm(
      undefined,
      contract
    );

    // Replace the current form
    this.form = newForm;

    console.log('Form created with values:', this.form.value);
    console.log('Form controls:', {
      basicInfo: this.form.get('basicInfo')?.value,
      officeSelection: this.form.get('officeSelection')?.value,
      leaseDetails: this.form.get('leaseDetails')?.value,
      additionalServices: this.form.get('additionalServices')?.value,
    });

    // Handle office selection if present
    if (contract.officeId && contract.officeId !== '') {
      this.handleOfficeSelection(contract.officeId);
    }

    // Force change detection to ensure the template updates
    this.cdr.detectChanges();

    setTimeout(() => {
      this.loading = false;
      this.cdr.detectChanges();
    }, 100);
  }

  private handleOfficeSelection(officeId: string): void {
    console.log('handleOfficeSelection called with officeId:', officeId);
    this.contractService
      .getAllOffices()
      .pipe(takeUntil(this.destroy$))
      .subscribe((offices) => {
        console.log('Available offices:', offices);
        const selectedOffice = offices.find(
          (office) => office.officeId === officeId
        );
        console.log('Found selected office:', selectedOffice);
        if (selectedOffice) {
          this.selectedBranchId = selectedOffice.branchId;
          this.updateAvailableOffices();

          // Update the form with the office selection data
          this.form.patchValue({
            officeSelection: {
              selectedOfficeId: officeId,
              selectedBranchId: selectedOffice.branchId,
              branchId: selectedOffice.branchId,
              officeNumber: selectedOffice.officeNumber,
            },
          });

          console.log(
            'Form patched with office selection:',
            this.form.get('officeSelection')?.value
          );
          this.cdr.detectChanges();
        }
      });
  }

  private initializeWithOffice(): void {
    if (this.selectedOffice) {
      this.form = this.tenantFormService.createTenantContractForm(
        this.selectedOffice
      );
      this.selectedBranchId = this.selectedOffice.branchId;
      this.updateAvailableOffices();
      // Don't skip any steps - user still needs to fill basic info first
    }
  }

  onBranchSelect(branchId: string): void {
    this.selectedBranchId = branchId;
    this.updateAvailableOffices();
    // Clear office selection when branch changes
    this.form.patchValue({
      officeSelection: {
        selectedOfficeId: '',
        officeNumber: '',
        branchId: branchId,
        selectedBranchId: branchId,
      },
    });
  }

  updateAvailableOffices(): void {
    if (this.selectedBranchId) {
      this.availableOffices$ = this.contractService.getAvailableOffices(
        this.selectedBranchId
      );
    } else {
      this.availableOffices$ = this.contractService.getAvailableOffices();
    }
  }

  onOfficeSelect(office: Office): void {
    this.form.patchValue({
      officeSelection: {
        selectedOfficeId: office.officeId,
        officeNumber: office.officeNumber,
        branchId: office.branchId,
        selectedBranchId: office.branchId,
      },
    });
  }

  onOfficeSelectById(officeId: string): void {
    this.contractService
      .getAllOffices()
      .pipe(takeUntil(this.destroy$))
      .subscribe((offices) => {
        const selectedOffice = offices.find(
          (office) => office.officeId === officeId
        );
        if (selectedOffice) {
          this.onOfficeSelect(selectedOffice);
        }
      });
  }

  nextStep(): void {
    if (!this.isCurrentStepValid()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields before proceeding',
      });
      return;
    }

    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++;
      this.saveDraft();
    }
  }

  prevStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  async saveDraft(): Promise<void> {
    if (this.saving) return;

    this.saving = true;
    try {
      const contractData = this.tenantFormService.extractContractFromForm(
        this.form.value
      );
      console.log('Saving contract data:', contractData); // Debug log

      if (!this.currentContract) {
        // Create new draft contract (office selection is optional)
        const officeId = this.form.value.officeSelection?.selectedOfficeId;

        const contractId = await this.contractService.createDraftContract(
          officeId, // Can be undefined for initial drafts
          { ...contractData, currentStep: this.currentStep }
        );

        this.contractId = contractId;
        this.contractCreated.emit(contractId);

        // Load the created contract
        this.loadExistingContract();
      } else {
        // Update existing draft
        await this.contractService.saveDraftContract(
          this.currentContract.id!,
          contractData,
          this.currentStep
        );
      }

      this.messageService.add({
        severity: 'success',
        summary: 'Saved',
        detail: 'Draft saved successfully',
      });
    } catch (error) {
      console.error('Error saving draft:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to save draft',
      });
    } finally {
      this.saving = false;
    }
  }

  async finalizeContract(): Promise<void> {
    if (!this.currentContract || !this.isFormValid()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please complete all required fields',
      });
      return;
    }

    // Check if office is selected before finalizing
    const officeId = this.form.value.officeSelection?.selectedOfficeId;
    if (!officeId) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Office Required',
        detail: 'Please select an office before finalizing the contract',
      });
      return;
    }

    this.loading = true;
    try {
      // Save final data
      const contractData = this.tenantFormService.extractContractFromForm(
        this.form.value
      );

      await this.contractService.saveDraftContract(
        this.currentContract.id!,
        contractData,
        this.currentStep
      );

      // Finalize the contract
      await this.contractService.finalizeContract(this.currentContract.id!);

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Contract finalized successfully',
      });

      this.contractFinalized.emit(this.currentContract.id!);
      this.closeDialog();
    } catch (error) {
      console.error('Error finalizing contract:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to finalize contract',
      });
    } finally {
      this.loading = false;
    }
  }

  async cancelDraft(): Promise<void> {
    if (!this.currentContract) return;

    this.loading = true;
    try {
      await this.contractService.cancelDraftContract(this.currentContract.id!);
      this.messageService.add({
        severity: 'info',
        summary: 'Cancelled',
        detail: 'Draft contract cancelled',
      });
      this.closeDialog();
    } catch (error) {
      console.error('Error cancelling draft:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to cancel draft',
      });
    } finally {
      this.loading = false;
    }
  }

  closeDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.resetForm();
  }

  private resetForm(): void {
    this.currentStep = 0;
    this.currentContract = undefined;
    this.contractId = undefined;
    this.form = this.tenantFormService.createTenantContractForm();
  }

  private isCurrentStepValid(): boolean {
    return this.tenantFormService.isStepValid(this.form, this.currentStep);
  }

  private isFormValid(): boolean {
    return this.form.valid;
  }

  get isFirstStep(): boolean {
    return this.currentStep === 0;
  }

  get isLastStep(): boolean {
    return this.currentStep === this.steps.length - 1;
  }

  get canProceed(): boolean {
    return this.isCurrentStepValid() && !this.loading && !this.saving;
  }
}
