.resources-list {
    display: flex;
    flex-direction: column;

    .resource-item {
        vertical-align: middle;
        align-items: center;
        margin-bottom: 14px;
        .image-box {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            margin-right: 8px;
            overflow: hidden;
            background: #e7e8e9;
            display: flex;
            vertical-align: middle;
            align-items: center;
            justify-content: center;
            text-align: center;
            img {
                width: 80%;
                height: 80%;
                object-fit: cover;
            }
        }

        .resource-details {
            height: 100%;
            display: flex;
            vertical-align: middle;
            align-items: center;
            .resource-color {
                right: 10px;
                top: 0;
                bottom: 0;
                position: absolute;
                width: 80px;
                height: 80px;
                margin: auto 0;
            }
        }
        
        &:hover {
            .delete-resource-btn {
                display: block !important;
            }
        }
    }
}

.delete-resource-btn {
    top: 10px;
    right: 10px;
    position: absolute;
    background: #ffeded;
    display: none;
    &::after {
        display: none;
    }
}
:host ::ng-deep {
    .p-dropdown {
        width: 100%;
    }
}

.list-group-item {
    position: relative;
    display: flex;
    vertical-align: middle;
    align-items: center;
}