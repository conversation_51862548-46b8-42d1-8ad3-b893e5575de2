import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { ConfirmationService, MessageService } from 'primeng/api';
interface Branch {
  name: string;
  address: string;
  // Add other branch properties here
  [key: string]: any; // For any additional dynamic properties
}
@Component({
  selector: 'app-branches',
  templateUrl: './branches.component.html',
  styleUrls: ['./branches.component.scss'],
  providers: [ConfirmationService, MessageService],
})
export class BranchesComponent implements OnInit {
  branches: any[] = [];
  addBranchView: boolean = false;
  currentUserBranchId: string | null = null;
  isRegularUser: boolean = false;
  expandedRowKeys: any = {};

  constructor(
    private afs: AngularFirestore,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private afAuth: AngularFireAuth
  ) {}

expandedBranchId: string | null = null;

ngOnInit() {
  this.checkUserType();
  if (this.isRegularUser && this.branches.length > 0) {
    this.expandedBranchId = this.branches[0].docid; // Auto-expand first branch
  }
}

  getBranches(userId: string) {
    // Query for branches where 'uid' is user.uid
    const query1 = this.afs.collection('branches', ref => ref.where('uid', '==', userId)).snapshotChanges();

    // Query for branches where 'managerId' is user.uid
    const query2 = this.afs.collection('branches', ref => ref.where('managerId', '==', userId)).snapshotChanges();

    // Combine the results of both queries
    query1.subscribe((changes1) => {
      const branches1 = changes1.map((change) => ({
        ...change.payload.doc.data(),
        docid: change.payload.doc.id
      }));

      query2.subscribe((changes2) => {
        const branches2 = changes2.map((change) => ({
          ...change.payload.doc.data(),
          docid: change.payload.doc.id
        }));

        // Combine the two arrays and remove duplicates
        const combinedBranches = [...branches1, ...branches2];
        this.branches = this.removeDuplicates(combinedBranches, 'docid');
      });
    });
  }

  removeDuplicates(arr: any[], key: string) {
    const seen = new Set();
    return arr.filter(item => {
      const k = item[key];
      return seen.has(k) ? false : seen.add(k);
    });
  }


getSingleBranch(branchId: string) {
  this.afs.collection<Branch>('branches').doc(branchId).get().subscribe((branch) => {
    if (branch.exists) {
      const branchData = branch.data();
      if (branchData) {
        this.branches = [{ ...branchData, docid: branch.id }];
      }
    }
  });
}

  checkUserType() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs.collection('users').doc(user.uid).get().subscribe((userDoc) => {
          const userData = userDoc.data() as any;

          // Check if user has a branchId (regular user)
          if (userData?.branchId) {
            this.currentUserBranchId = userData.branchId;
            this.isRegularUser = true;
            this.getSingleBranch(userData.branchId);
          } else {
            // User is admin/manager - get all branches
            this.isRegularUser = false;
            this.getBranches(user.uid);
          }
        });
      }
    });
  }

  confirm(event: Event, id: any) {
    console.log(id);
    this.confirmationService.confirm({
      target: event.target!,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.afAuth.authState.subscribe((user) => {
          if (user) {
            this.afs
              .collection('branches')
              .doc(id)
              .delete()
              .then(() => {
                this.messageService.add({
                  severity: 'success',
                  summary: 'Success',
                  detail: 'Branch deleted successfully!',
                });
              })
              .catch((error) => {
                console.error('Error deleting document: ', error);
              });
          }
        });
      },
      reject: () => {
        this.messageService.add({
          severity: 'warn',
          summary: 'Cancelled',
          detail: 'Operation canceled',
        });
      },
    });
  }
}
