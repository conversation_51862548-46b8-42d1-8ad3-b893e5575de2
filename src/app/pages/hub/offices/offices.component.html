<p-toolbar>
  <div class="p-toolbar-group-left">
    <h4 class="ms-3 mt-2">Offices</h4>
  </div>

  <div class="p-toolbar-group-right">
    <p-button
      icon="pi pi-plus"
      label="New Office"
      class="me-2"
      data-bs-toggle="offcanvas"
      data-bs-target="#officesCanvas"
      aria-controls="officesExample"
    ></p-button>
    <p-button
      icon="bi bi-back"
      label="Sectors"
      styleClass="p-button-success me-2"
      data-bs-toggle="offcanvas"
      data-bs-target="#sectorsCanvas"
      aria-controls="sectorsExample"
    ></p-button>
    <p-button
      icon="bi bi-boxes"
      label="Resources"
      styleClass="p-button-danger"
      data-bs-toggle="offcanvas"
      data-bs-target="#resourcesCanvas"
      aria-controls="resourcesExample"
    ></p-button>
  </div>
</p-toolbar>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="officesCanvas"
  aria-labelledby="officesCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="officesCanvasLabel">New Office</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body">
    Lorem ipsum dolor sit amet consectetur adipisicing elit. Laborum adipisci
    quidem maxime dolorem tempora culpa, porro dolore commodi iusto provident,
    explicabo, fuga fugiat? Asperiores tempore iste natus autem vero quos.
  </div>
</div>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="sectorsCanvas"
  aria-labelledby="sectorsCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="sectorsCanvasLabel">Sectors</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body">
    <form>
      <div class="mb-3">
        <p-dropdown
          [options]="branches"
          name="branch"
          [(ngModel)]="selectedBranch"
          placeholder="Select a Branch first"
          optionLabel="name"
          [showClear]="true"
          (onChange)="getSectors()"
        ></p-dropdown>
      </div>
      <div
        class="mb-3"
        *ngIf="selectedBranch !== undefined && selectedBranch !== null"
      >
        <label for="sectorName" class="form-label">Sector Name</label>
        <input
          type="text"
          class="form-control"
          name="name"
          [(ngModel)]="sectorName"
          id="sectorName"
          placeholder="A-01"
        />
      </div>
      <div
        class="col-12 text-end"
        *ngIf="selectedBranch !== undefined && selectedBranch !== null"
      >
        <button
          pButton
          pRipple
          type="button"
          (click)="addSector()"
          label="Save"
          class=""
        ></button>
      </div>
    </form>
    <hr *ngIf="selectedBranch !== undefined && selectedBranch !== null">

    <ul class="list-group list-group-flush">
      <li class="list-group-item" *ngFor="let sect of branchSectors">
        <button pButton pRipple type="button" icon="pi pi-times" class="p-button-raised p-button-rounded me-3 p-button-danger p-button-text"></button>
        <strong>{{sect.name}}</strong>
      </li>
    </ul>

  </div>
</div>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="resourcesCanvas"
  aria-labelledby="resourcesCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="resourcesCanvasLabel">
      Office Resources Types
    </h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body">
    <form [formGroup]="resourceForm" (ngSubmit)="addResourceType()">
      <div class="col-12 pb-0 d-flex">
        <div
          class="shadow"
          style="
            border-radius: 8px;
            min-width: 100px;
            width: 150px;
            background: #f7f8ff;
            min-height: 100px;
            height: 150px;
          "
        ></div>
        <div class="col ps-3">
          <div class="mb-3">
            <label for="name" class="form-label">Name</label>
            <input
              type="text"
              class="form-control"
              formControlName="name"
              id="name"
              placeholder="Chair"
            />
          </div>
          <div class="mb-3">
            <label for="color" class="form-label">Color</label>
            <input
              type="color"
              style="height: 54px"
              formControlName="color"
              class="form-control"
              id="color"
            />
          </div>
          <div class="mb-3 text-end">
            <button
              pButton
              pRipple
              type="submit"
              label="Save"
              class=""
            ></button>
          </div>
        </div>
      </div>
    </form>
    <hr class="m-0" />

    <div class="resources-list col-12">
      <div
        class="resource-item relative shadow d-flex"
        *ngFor="let r of resources"
      >
        <div class="image-box">
          <img src="{{ r.image }}" alt="{{ r.name }}" />
        </div>
        <div class="resource-details col relative">
          <h4>{{ r.name }}</h4>
          <div class="resource-color" [ngStyle]="{ background: r.color }"></div>
        </div>
        <button
          pButton
          pRipple
          type="button"
          label=""
          icon="pi pi-times"
          class="delete-resource-btn p-button-rounded p-button-danger p-button-outlined"
        ></button>
      </div>
    </div>
  </div>
</div>
