export interface Tenant {
  id?: string; // For edits
  basicInfo: {
    fullName: string;
    email: string;
    phone: string;
    companyName?: string;
    jobTitle?: string;
    idNumber?: string;
    taxId?: string;
  };
  contactPreferences: {
    preferredCommunication: 'Email' | 'Phone';
    emergencyContact: {
      name: string;
      phone: string;
    };
  };
  leaseDetails: {
    property: {
      buildingId: string; // Dropdown value
      floor: string;
      deskNumber?: string;
    };
    terms: {
      startDate: Date;
      endDate: Date;
      rentalType: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
      rentAmount: number;
      currency: string;
      securityDeposit: number;
      paymentDueDay: number; // e.g., 1 for 1st of month
      lateFeePolicy: string;
    };
    paymentMethod: {
      type: 'Bank Transfer' | 'Credit Card' | 'Other';
      billingCycle: 'Monthly' | 'Quarterly';
      autoPay: boolean;
    };
  };
  additionalServices: {
    internet: boolean;
    cleaning: boolean;
    meetingRoom: boolean;
    parking: boolean;
    mailHandling: boolean;
  };
  legal: {
    agreementSigned: boolean;
    termsAccepted: boolean;
    privacyConsent: boolean;
  };
  adminNotes: {
    specialRequests?: string;
    moveInChecklist: string[];
  };
}

export interface Office {
  officeId: string;
  officeNumber: number;
  branchId: string;
  status: 'available' | 'occupied' | 'under_processing' | 'maintenance';
  capacity?: number;
  amenities?: string[];
  monthlyRate?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum ContractStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

export interface Contract {
  id?: string;
  contractNumber: string;
  status: ContractStatus;
  officeId: string;
  tenant?: Tenant;
  startDate?: Date;
  endDate?: Date;
  rentalType: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
  rentAmount?: number;
  currency: string;
  securityDeposit?: number;
  paymentDueDay?: number;
  lateFeePolicy?: string;
  paymentMethod?: {
    type: 'Bank Transfer' | 'Credit Card' | 'Other';
    billingCycle: 'Monthly' | 'Quarterly';
    autoPay: boolean;
  };
  additionalServices?: {
    internet: boolean;
    cleaning: boolean;
    meetingRoom: boolean;
    parking: boolean;
    mailHandling: boolean;
  };
  legal?: {
    agreementSigned: boolean;
    termsAccepted: boolean;
    privacyConsent: boolean;
  };
  adminNotes?: {
    specialRequests?: string;
    moveInChecklist: string[];
  };
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
  lastModifiedBy?: string;
  currentStep?: number;
}
