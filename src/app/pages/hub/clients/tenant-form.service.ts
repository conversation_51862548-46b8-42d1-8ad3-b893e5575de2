import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Tenant, Contract, Office } from './tenant.model';

@Injectable({
  providedIn: 'root',
})
export class TenantFormService {
  constructor(private fb: FormBuilder) {}

  // Create form for new tenant/contract creation with office selection
  createTenantContractForm(
    selectedOffice?: Office,
    contract?: Contract
  ): FormGroup {
    console.log('Creating form with contract data:', contract); // Debug log
    console.log('Contract tenant data:', contract?.tenant); // Debug log
    console.log('Contract basic info:', contract?.tenant?.basicInfo); // Debug log
    return this.fb.group({
      // Basic Info (step 0 - now first step)
      basicInfo: this.fb.group({
        fullName: [
          contract?.tenant?.basicInfo?.fullName || '',
          Validators.required,
        ],
        email: [
          contract?.tenant?.basicInfo?.email || '',
          [Validators.required, Validators.email],
        ],
        phone: [contract?.tenant?.basicInfo?.phone || '', Validators.required],
        companyName: [contract?.tenant?.basicInfo?.companyName || ''],
        jobTitle: [contract?.tenant?.basicInfo?.jobTitle || ''],
        idNumber: [contract?.tenant?.basicInfo?.idNumber || ''],
        taxId: [contract?.tenant?.basicInfo?.taxId || ''],
      }),

      // Contact Preferences (step 0 continued)
      contactPreferences: this.fb.group({
        preferredCommunication: [
          contract?.tenant?.contactPreferences?.preferredCommunication ||
            'Email',
        ],
        emergencyContact: this.fb.group({
          name: [
            contract?.tenant?.contactPreferences?.emergencyContact?.name || '',
          ],
          phone: [
            contract?.tenant?.contactPreferences?.emergencyContact?.phone || '',
          ],
        }),
      }),

      // Office selection (step 1 - now second step)
      officeSelection: this.fb.group({
        selectedOfficeId: [
          selectedOffice?.officeId || contract?.officeId || '',
          // Remove required validation for now since office selection is optional for drafts
        ],
        officeNumber: [selectedOffice?.officeNumber || ''],
        branchId: [selectedOffice?.branchId || ''],
        selectedBranchId: [''], // For branch selection
      }),

      // Lease Details (step 2)
      leaseDetails: this.fb.group({
        terms: this.fb.group({
          startDate: [contract?.startDate || '', Validators.required],
          endDate: [contract?.endDate || '', Validators.required],
          rentalType: [contract?.rentalType || 'Monthly'],
          rentAmount: [
            contract?.rentAmount || 0,
            [Validators.required, Validators.min(1)],
          ],
          currency: [contract?.currency || 'USD'],
          securityDeposit: [contract?.securityDeposit || 0],
          paymentDueDay: [contract?.paymentDueDay || 1],
          lateFeePolicy: [contract?.lateFeePolicy || ''],
        }),
        paymentMethod: this.fb.group({
          type: [contract?.paymentMethod?.type || 'Bank Transfer'],
          billingCycle: [contract?.paymentMethod?.billingCycle || 'Monthly'],
          autoPay: [contract?.paymentMethod?.autoPay || false],
        }),
      }),

      // Additional Services (step 3)
      additionalServices: this.fb.group({
        internet: [contract?.additionalServices?.internet || false],
        cleaning: [contract?.additionalServices?.cleaning || false],
        meetingRoom: [contract?.additionalServices?.meetingRoom || false],
        parking: [contract?.additionalServices?.parking || false],
        mailHandling: [contract?.additionalServices?.mailHandling || false],
      }),

      // Legal (step 4 - Review & Confirmation)
      legal: this.fb.group({
        agreementSigned: [
          contract?.legal?.agreementSigned || false,
          Validators.requiredTrue,
        ],
        termsAccepted: [
          contract?.legal?.termsAccepted || false,
          Validators.requiredTrue,
        ],
        privacyConsent: [
          contract?.legal?.privacyConsent || false,
          Validators.requiredTrue,
        ],
      }),

      // Admin Notes
      adminNotes: this.fb.group({
        specialRequests: [contract?.adminNotes?.specialRequests || ''],
        moveInChecklist: this.fb.array(
          contract?.adminNotes?.moveInChecklist
            ? contract.adminNotes.moveInChecklist.map((item) =>
                this.fb.control(item)
              )
            : []
        ),
      }),
    });
  }

  createTenantForm(tenant?: Tenant): FormGroup {
    return this.fb.group({
      basicInfo: this.fb.group({
        fullName: [tenant?.basicInfo.fullName || '', Validators.required],
        email: [
          tenant?.basicInfo.email || '',
          [Validators.required, Validators.email],
        ],
        phone: [tenant?.basicInfo.phone || '', Validators.required],
        companyName: [tenant?.basicInfo.companyName || ''],
        jobTitle: [tenant?.basicInfo.jobTitle || ''],
        idNumber: [tenant?.basicInfo.idNumber || ''],
        taxId: [tenant?.basicInfo.taxId || ''],
      }),
      contactPreferences: this.fb.group({
        preferredCommunication: [
          tenant?.contactPreferences.preferredCommunication || 'Email',
        ],
        emergencyContact: this.fb.group({
          name: [tenant?.contactPreferences.emergencyContact.name || ''],
          phone: [tenant?.contactPreferences.emergencyContact.phone || ''],
        }),
      }),
      leaseDetails: this.fb.group({
        property: this.fb.group({
          buildingId: [
            tenant?.leaseDetails.property.buildingId || '',
            Validators.required,
          ],
          floor: [tenant?.leaseDetails.property.floor || ''],
          deskNumber: [tenant?.leaseDetails.property.deskNumber || ''],
        }),
        terms: this.fb.group({
          startDate: [
            tenant?.leaseDetails.terms.startDate || '',
            Validators.required,
          ],
          endDate: [
            tenant?.leaseDetails.terms.endDate || '',
            Validators.required,
          ],
          rentalType: [tenant?.leaseDetails.terms.rentalType || 'Monthly'],
          rentAmount: [
            tenant?.leaseDetails.terms.rentAmount || 0,
            [Validators.required, Validators.min(1)],
          ],
          currency: [tenant?.leaseDetails.terms.currency || 'USD'],
          securityDeposit: [tenant?.leaseDetails.terms.securityDeposit || 0],
          paymentDueDay: [tenant?.leaseDetails.terms.paymentDueDay || 1],
          lateFeePolicy: [tenant?.leaseDetails.terms.lateFeePolicy || ''],
        }),
        paymentMethod: this.fb.group({
          type: [tenant?.leaseDetails.paymentMethod.type || 'Bank Transfer'],
          billingCycle: [
            tenant?.leaseDetails.paymentMethod.billingCycle || 'Monthly',
          ],
          autoPay: [tenant?.leaseDetails.paymentMethod.autoPay || false],
        }),
      }),
      additionalServices: this.fb.group({
        internet: [tenant?.additionalServices.internet || false],
        cleaning: [tenant?.additionalServices.cleaning || false],
        meetingRoom: [tenant?.additionalServices.meetingRoom || false],
        parking: [tenant?.additionalServices.parking || false],
        mailHandling: [tenant?.additionalServices.mailHandling || false],
      }),
      legal: this.fb.group({
        agreementSigned: [
          tenant?.legal.agreementSigned || false,
          Validators.requiredTrue,
        ],
        termsAccepted: [
          tenant?.legal.termsAccepted || false,
          Validators.requiredTrue,
        ],
        privacyConsent: [
          tenant?.legal.privacyConsent || false,
          Validators.requiredTrue,
        ],
      }),
      adminNotes: this.fb.group({
        specialRequests: [tenant?.adminNotes.specialRequests || ''],
        moveInChecklist: this.fb.array(
          tenant?.adminNotes.moveInChecklist
            ? tenant.adminNotes.moveInChecklist.map((item) =>
                this.fb.control(item)
              )
            : []
        ),
      }),
    });
  }

  // Extract contract data from form
  extractContractFromForm(formValue: any): Partial<Contract> {
    return {
      officeId: formValue.officeSelection?.selectedOfficeId,
      startDate: formValue.leaseDetails?.terms?.startDate,
      endDate: formValue.leaseDetails?.terms?.endDate,
      rentalType: formValue.leaseDetails?.terms?.rentalType,
      rentAmount: formValue.leaseDetails?.terms?.rentAmount,
      currency: formValue.leaseDetails?.terms?.currency,
      securityDeposit: formValue.leaseDetails?.terms?.securityDeposit,
      paymentDueDay: formValue.leaseDetails?.terms?.paymentDueDay,
      lateFeePolicy: formValue.leaseDetails?.terms?.lateFeePolicy,
      paymentMethod: formValue.leaseDetails?.paymentMethod,
      additionalServices: formValue.additionalServices,
      legal: formValue.legal,
      adminNotes: formValue.adminNotes,
      // Include tenant data for complete contract storage
      tenant: this.extractTenantFromForm(formValue) as Tenant,
    };
  }

  // Extract tenant data from form
  extractTenantFromForm(formValue: any): Partial<Tenant> {
    const tenantData = {
      basicInfo: formValue.basicInfo,
      contactPreferences: formValue.contactPreferences,
      leaseDetails: {
        property: {
          buildingId: formValue.officeSelection?.branchId || '',
          floor: '', // You can add floor selection if needed
          deskNumber: formValue.officeSelection?.officeNumber?.toString() || '',
        },
        terms: formValue.leaseDetails?.terms,
        paymentMethod: formValue.leaseDetails?.paymentMethod,
      },
      additionalServices: formValue.additionalServices,
      legal: formValue.legal,
      adminNotes: formValue.adminNotes,
    };
    console.log('Extracted tenant data:', tenantData); // Debug log
    return tenantData;
  }

  // Validation methods
  isStepValid(form: FormGroup, step: number): boolean {
    switch (step) {
      case 0: // Basic Info
        return form.get('basicInfo')?.valid || false;
      case 1: // Office Selection
        return true; // Make office selection optional for drafts
      case 2: // Lease Details
        return form.get('leaseDetails')?.valid || false;
      case 3: // Additional Services
        return true; // Optional step
      case 4: // Review & Confirm
        return form.get('legal')?.valid || false;
      default:
        return false;
    }
  }
}
