<div class="no-offices text-center" *ngIf="offices.length === 0">
  <div>
    <h6>
      No Offices found for
      <strong class="violet-text">{{ branch.name }}</strong>
    </h6>
    <i class="bi bi-journal-x text-muted"></i>
    <p>You can start Add Offices one by one or buld under your branch!</p>
    <div *ngIf="!isRegularUser">
      <button
        pButton
        pRipple
        label="Add One Office"
        class="p-button-sm me-3"
        (click)="addOneOffice = true"
      ></button>
      <button
        pButton
        pRipple
        label="Add Bulk Offices"
        class="p-button-secondary p-button-sm"
        (click)="addBulkOffice = true"
      ></button>
    </div>
  </div>
</div>
<div class="offices-details" *ngIf="offices.length !== 0">
  <div class="col-12 mb-3 text-end" *ngIf="!isRegularUser">
    <button
      pButton
      pRipple
      label="Add One Office"
      icon="pi pi-plus"
      class="p-button-sm me-3"
      (click)="addOneOffice = true"
    ></button>
    <button
      pButton
      pRipple
      label="Add Bulk Offices"
      icon="pi pi-plus"
      class="p-button-secondary p-button-sm"
      (click)="addBulkOffice = true"
    ></button>
  </div>
  <div class="offices-cards">
    <div
      class="card m-2 office-card border"
      (click)="showOfficeDetails(office)"
      [ngClass]="{
        'available-card': office.status === 'available',
        'occupied-card': office.status === 'occupied'
      }"
      *ngFor="let office of offices"
    >
      <h6 class="m-0">
        {{ office.officeNumber }}
      </h6>
    </div>
  </div>
</div>

<p-sidebar
  [(visible)]="openOfficeDetails"
  class="normal-sidebar"
  position="right"
  [baseZIndex]="10000"
>
  <app-office-item [office]="selectedOffice"></app-office-item>
</p-sidebar>

<p-sidebar
  [(visible)]="addOneOffice"
  class="normal-sidebar"
  position="right"
  [baseZIndex]="10000"
>
  <h3>Office Details</h3>

  <div class="field">
    <label for="officeNumber" class="block">Office Number</label>
    <input
      id="officeNumber"
      type="number"
      aria-describedby="officeNumber-help"
      pInputText
      [(ngModel)]="officeNumber"
    />
  </div>

  <button
    pButton
    pRipple
    label="Add One Office"
    class="p-button-sm me-3 mt-3"
    [disabled]="officeNumber === null || officeNumber === undefined"
    (click)="saveOneOffice()"
  ></button>
</p-sidebar>

<p-sidebar
  [(visible)]="addBulkOffice"
  class="normal-sidebar"
  position="right"
  [baseZIndex]="10000"
>
  <h3>Bulk Offices Details</h3>
  <div class="row mx-0 col-12 justify-content-between">
    <div class="field px-0">
      <label for="startNumber" class="block">Office Number</label>
      <input
        id="startNumber"
        type="number"
        aria-describedby="startNumber-help"
        pInputText
        [(ngModel)]="startNumber"
      />
    </div>
    <div class="field px-0">
      <label for="lastNumber" class="block">Office Number</label>
      <input
        id="lastNumber"
        type="number"
        aria-describedby="lastNumber-help"
        pInputText
        [(ngModel)]="lastNumber"
      />
    </div>
  </div>

  <button
    pButton
    pRipple
    label="Add One Office"
    class="p-button-sm me-3 mt-3"
    (click)="saveBulkOffice()"
    [disabled]="
      (startNumber === null || startNumber === undefined) &&
      (lastNumber === null || lastNumber === undefined)
    "
  ></button>
</p-sidebar>
