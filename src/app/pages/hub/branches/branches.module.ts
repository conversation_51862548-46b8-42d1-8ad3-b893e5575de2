import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BranchesRoutingModule } from './branches-routing.module';
import { BranchesComponent } from './branches.component';
import { PrimengModule } from 'src/app/shared/modules/primeng.module';
import { AddBranchComponent } from './add-branch/add-branch.component';
import { DetailsComponent } from './details/details.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { OfficeItemComponent } from './details/office-item/office-item.component';


@NgModule({
  declarations: [
    BranchesComponent,
    AddBranchComponent,
    DetailsComponent,
    OfficeItemComponent
  ],
  imports: [
    CommonModule,
    BranchesRoutingModule,
    PrimengModule,
    GoogleMapsModule
  ]
})
export class BranchesModule { }
