<p-dialog
  [(visible)]="visible"
  [modal]="true"
  [closable]="true"
  [draggable]="false"
  [resizable]="false"
  styleClass="tenant-form-dialog"
  [style]="{ width: '90vw', maxWidth: '1000px', height: '90vh' }"
  header="New Tenant Contract"
  (onHide)="closeDialog()"
>
  <div class="tenant-form-container" [formGroup]="form">
    <!-- Progress Steps -->
    <div class="steps-container mb-4">
      <p-steps
        [model]="steps"
        [activeIndex]="currentStep"
        [readonly]="true"
        styleClass="custom-steps"
      ></p-steps>
    </div>

    <!-- Loading Overlay -->
    <div *ngIf="loading" class="loading-overlay">
      <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
    </div>

    <!-- Step Content -->
    <div class="step-content" *ngIf="!loading">
      <!-- Step 0: Basic Information -->
      <div *ngIf="currentStep === 0" class="step-panel">
        <h4>Basic Information</h4>

        <!-- Basic Info -->
        <div formGroupName="basicInfo" class="form-section">
          <h5>Personal Details</h5>
          <div class="p-fluid p-formgrid p-grid">
            <div class="p-field p-col-12 p-md-6">
              <label for="fullName">Full Name *</label>
              <input
                pInputText
                id="fullName"
                formControlName="fullName"
                placeholder="Enter full name"
              />
            </div>
            <div class="p-field p-col-12 p-md-6">
              <label for="email">Email *</label>
              <input
                pInputText
                id="email"
                formControlName="email"
                placeholder="Enter email address"
                type="email"
              />
            </div>
            <div class="p-field p-col-12 p-md-6">
              <label for="phone">Phone *</label>
              <input
                pInputText
                id="phone"
                formControlName="phone"
                placeholder="Enter phone number"
              />
            </div>
            <div class="p-field p-col-12 p-md-6">
              <label for="companyName">Company Name</label>
              <input
                pInputText
                id="companyName"
                formControlName="companyName"
                placeholder="Enter company name"
              />
            </div>
            <div class="p-field p-col-12 p-md-6">
              <label for="jobTitle">Job Title</label>
              <input
                pInputText
                id="jobTitle"
                formControlName="jobTitle"
                placeholder="Enter job title"
              />
            </div>
            <div class="p-field p-col-12 p-md-6">
              <label for="idNumber">ID Number</label>
              <input
                pInputText
                id="idNumber"
                formControlName="idNumber"
                placeholder="Enter ID number"
              />
            </div>
          </div>
        </div>

        <!-- Contact Preferences -->
        <div formGroupName="contactPreferences" class="form-section">
          <h5>Contact Preferences</h5>
          <div class="p-fluid p-formgrid p-grid">
            <div class="p-field p-col-12 p-md-6">
              <label for="preferredCommunication"
                >Preferred Communication</label
              >
              <p-dropdown
                id="preferredCommunication"
                formControlName="preferredCommunication"
                [options]="[
                  { label: 'Email', value: 'Email' },
                  { label: 'Phone', value: 'Phone' }
                ]"
                placeholder="Select communication method"
              ></p-dropdown>
            </div>
          </div>

          <div formGroupName="emergencyContact">
            <h6>Emergency Contact</h6>
            <div class="p-fluid p-formgrid p-grid">
              <div class="p-field p-col-12 p-md-6">
                <label for="emergencyName">Name</label>
                <input
                  pInputText
                  id="emergencyName"
                  formControlName="name"
                  placeholder="Emergency contact name"
                />
              </div>
              <div class="p-field p-col-12 p-md-6">
                <label for="emergencyPhone">Phone</label>
                <input
                  pInputText
                  id="emergencyPhone"
                  formControlName="phone"
                  placeholder="Emergency contact phone"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 1: Office Selection -->
      <div *ngIf="currentStep === 1" class="step-panel">
        <h4>Select Office</h4>

        <div formGroupName="officeSelection">
          <!-- Branch Selection -->
          <div class="field">
            <label for="branch">Select Branch *</label>
            <p-dropdown
              id="branch"
              [options]="(branches$ | async) || []"
              formControlName="selectedBranchId"
              optionLabel="name"
              optionValue="docid"
              placeholder="Select a branch first"
              [showClear]="true"
              (onChange)="onBranchSelect($event.value)"
              styleClass="w-100"
            >
              <ng-template pTemplate="selectedItem" let-branch>
                <div *ngIf="branch">
                  {{ branch.name }} - {{ branch.address }}
                </div>
              </ng-template>
              <ng-template pTemplate="item" let-branch>
                <div class="branch-item">
                  <div class="branch-name">{{ branch.name }}</div>
                  <div class="branch-address">
                    <small>{{ branch.address }}</small>
                  </div>
                </div>
              </ng-template>
            </p-dropdown>
          </div>

          <!-- Office Selection -->
          <div class="field" *ngIf="selectedBranchId">
            <label for="office">Available Offices *</label>
            <p-dropdown
              id="office"
              [options]="(availableOffices$ | async) || []"
              formControlName="selectedOfficeId"
              optionLabel="officeNumber"
              optionValue="officeId"
              placeholder="Select an office"
              [showClear]="true"
              (onChange)="onOfficeSelectById($event.value)"
              styleClass="w-100"
            >
              <ng-template pTemplate="selectedItem" let-office>
                <div *ngIf="office">Office #{{ office.officeNumber }}</div>
              </ng-template>
              <ng-template pTemplate="item" let-office>
                <div class="office-item">
                  <div class="office-number">
                    Office #{{ office.officeNumber }}
                  </div>
                  <div class="office-details">
                    <span
                      class="office-status"
                      [class]="'status-' + office.status"
                    >
                      {{ office.status | titlecase }}
                    </span>
                  </div>
                </div>
              </ng-template>
            </p-dropdown>
          </div>

          <!-- Office Info Display -->
          <div
            *ngIf="form.value.officeSelection?.selectedOfficeId"
            class="office-info mt-3"
          >
            <div class="p-card">
              <div class="p-card-body">
                <h6>Selected Office Details</h6>
                <p>
                  <strong>Office Number:</strong> #{{
                    form.value.officeSelection?.officeNumber
                  }}
                </p>
                <p>
                  <strong>Branch:</strong>
                  {{ form.value.officeSelection?.branchId }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Lease Details -->
      <div *ngIf="currentStep === 2" class="step-panel">
        <h4>Lease Details</h4>

        <div formGroupName="leaseDetails">
          <!-- Terms -->
          <div formGroupName="terms" class="form-section">
            <h5>Lease Terms</h5>
            <div class="p-fluid p-formgrid p-grid">
              <div class="p-field p-col-12 p-md-6">
                <label for="startDate">Start Date *</label>
                <p-calendar
                  id="startDate"
                  formControlName="startDate"
                  [showIcon]="true"
                  dateFormat="mm/dd/yy"
                ></p-calendar>
              </div>
              <div class="p-field p-col-12 p-md-6">
                <label for="endDate">End Date *</label>
                <p-calendar
                  id="endDate"
                  formControlName="endDate"
                  [showIcon]="true"
                  dateFormat="mm/dd/yy"
                ></p-calendar>
              </div>
              <div class="p-field p-col-12 p-md-4">
                <label for="rentalType">Rental Type</label>
                <p-dropdown
                  id="rentalType"
                  formControlName="rentalType"
                  [options]="[
                    { label: 'Daily', value: 'Daily' },
                    { label: 'Weekly', value: 'Weekly' },
                    { label: 'Monthly', value: 'Monthly' },
                    { label: 'Yearly', value: 'Yearly' }
                  ]"
                ></p-dropdown>
              </div>
              <div class="p-field p-col-12 p-md-4">
                <label for="rentAmount">Rent Amount *</label>
                <p-inputNumber
                  id="rentAmount"
                  formControlName="rentAmount"
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                ></p-inputNumber>
              </div>
              <div class="p-field p-col-12 p-md-4">
                <label for="securityDeposit">Security Deposit</label>
                <p-inputNumber
                  id="securityDeposit"
                  formControlName="securityDeposit"
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                ></p-inputNumber>
              </div>
            </div>
          </div>

          <!-- Payment Method -->
          <div formGroupName="paymentMethod" class="form-section">
            <h5>Payment Method</h5>
            <div class="p-fluid p-formgrid p-grid">
              <div class="p-field p-col-12 p-md-4">
                <label for="paymentType">Payment Type</label>
                <p-dropdown
                  id="paymentType"
                  formControlName="type"
                  [options]="[
                    { label: 'Bank Transfer', value: 'Bank Transfer' },
                    { label: 'Credit Card', value: 'Credit Card' },
                    { label: 'Other', value: 'Other' }
                  ]"
                ></p-dropdown>
              </div>
              <div class="p-field p-col-12 p-md-4">
                <label for="billingCycle">Billing Cycle</label>
                <p-dropdown
                  id="billingCycle"
                  formControlName="billingCycle"
                  [options]="[
                    { label: 'Monthly', value: 'Monthly' },
                    { label: 'Quarterly', value: 'Quarterly' }
                  ]"
                ></p-dropdown>
              </div>
              <div class="p-field p-col-12 p-md-4">
                <div class="field-checkbox">
                  <p-checkbox
                    id="autoPay"
                    formControlName="autoPay"
                    [binary]="true"
                  ></p-checkbox>
                  <label for="autoPay">Enable Auto Pay</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Additional Services -->
      <div *ngIf="currentStep === 3" class="step-panel">
        <h4>Additional Services</h4>

        <div formGroupName="additionalServices" class="form-section">
          <div class="p-fluid p-formgrid p-grid">
            <div class="p-field p-col-12 p-md-6">
              <div class="field-checkbox">
                <p-checkbox
                  id="internet"
                  formControlName="internet"
                  [binary]="true"
                ></p-checkbox>
                <label for="internet">Internet Access</label>
              </div>
            </div>
            <div class="p-field p-col-12 p-md-6">
              <div class="field-checkbox">
                <p-checkbox
                  id="cleaning"
                  formControlName="cleaning"
                  [binary]="true"
                ></p-checkbox>
                <label for="cleaning">Cleaning Service</label>
              </div>
            </div>
            <div class="p-field p-col-12 p-md-6">
              <div class="field-checkbox">
                <p-checkbox
                  id="meetingRoom"
                  formControlName="meetingRoom"
                  [binary]="true"
                ></p-checkbox>
                <label for="meetingRoom">Meeting Room Access</label>
              </div>
            </div>
            <div class="p-field p-col-12 p-md-6">
              <div class="field-checkbox">
                <p-checkbox
                  id="parking"
                  formControlName="parking"
                  [binary]="true"
                ></p-checkbox>
                <label for="parking">Parking Space</label>
              </div>
            </div>
            <div class="p-field p-col-12">
              <div class="field-checkbox">
                <p-checkbox
                  id="mailHandling"
                  formControlName="mailHandling"
                  [binary]="true"
                ></p-checkbox>
                <label for="mailHandling">Mail Handling Service</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Admin Notes -->
        <div formGroupName="adminNotes" class="form-section">
          <h5>Special Requests</h5>
          <div class="p-field">
            <label for="specialRequests">Special Requests</label>
            <textarea
              pInputTextarea
              id="specialRequests"
              formControlName="specialRequests"
              rows="3"
              placeholder="Any special requests or notes..."
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Step 4: Review & Confirmation -->
      <div *ngIf="currentStep === 4" class="step-panel">
        <h4>Review & Confirmation</h4>

        <!-- Contract Summary -->
        <div class="contract-summary">
          <div class="summary-section">
            <h5>Office Details</h5>
            <p>
              <strong>Office:</strong> #{{
                form.value.officeSelection?.officeNumber
              }}
            </p>
            <p>
              <strong>Branch:</strong>
              {{ form.value.officeSelection?.branchId }}
            </p>
          </div>

          <div class="summary-section">
            <h5>Tenant Information</h5>
            <p><strong>Name:</strong> {{ form.value.basicInfo?.fullName }}</p>
            <p><strong>Email:</strong> {{ form.value.basicInfo?.email }}</p>
            <p><strong>Phone:</strong> {{ form.value.basicInfo?.phone }}</p>
            <p *ngIf="form.value.basicInfo?.companyName">
              <strong>Company:</strong> {{ form.value.basicInfo?.companyName }}
            </p>
          </div>

          <div class="summary-section">
            <h5>Lease Terms</h5>
            <p>
              <strong>Period:</strong>
              {{ form.value.leaseDetails?.terms?.startDate | date }} -
              {{ form.value.leaseDetails?.terms?.endDate | date }}
            </p>
            <p>
              <strong>Rent:</strong>
              {{ form.value.leaseDetails?.terms?.rentAmount | currency }}
              {{ form.value.leaseDetails?.terms?.rentalType }}
            </p>
            <p>
              <strong>Security Deposit:</strong>
              {{ form.value.leaseDetails?.terms?.securityDeposit | currency }}
            </p>
          </div>
        </div>

        <!-- Legal Agreements -->
        <div formGroupName="legal" class="form-section">
          <h5>Legal Agreements</h5>
          <div class="legal-checkboxes">
            <div class="field-checkbox">
              <p-checkbox
                id="agreementSigned"
                formControlName="agreementSigned"
                [binary]="true"
              ></p-checkbox>
              <label for="agreementSigned"
                >I confirm that the lease agreement has been signed *</label
              >
            </div>
            <div class="field-checkbox">
              <p-checkbox
                id="termsAccepted"
                formControlName="termsAccepted"
                [binary]="true"
              ></p-checkbox>
              <label for="termsAccepted"
                >Terms and conditions have been accepted *</label
              >
            </div>
            <div class="field-checkbox">
              <p-checkbox
                id="privacyConsent"
                formControlName="privacyConsent"
                [binary]="true"
              ></p-checkbox>
              <label for="privacyConsent"
                >Privacy policy consent has been given *</label
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="form-navigation">
      <div class="nav-left">
        <button
          pButton
          type="button"
          label="Previous"
          icon="pi pi-chevron-left"
          class="p-button-secondary"
          [disabled]="isFirstStep || loading"
          (click)="prevStep()"
        ></button>
      </div>

      <div class="nav-center">
        <p-button
          type="button"
          label="Save Draft"
          icon="pi pi-save"
          styleClass="p-button-info"
          [disabled]="loading"
          [loading]="saving"
          (click)="saveDraft()"
        ></p-button>

        <button
          *ngIf="currentContract"
          pButton
          type="button"
          label="Cancel Draft"
          icon="pi pi-times"
          class="p-button-danger p-button-outlined"
          [disabled]="loading"
          (click)="cancelDraft()"
        ></button>
      </div>

      <div class="nav-right">
        <button
          *ngIf="!isLastStep"
          pButton
          type="button"
          label="Next"
          icon="pi pi-chevron-right"
          iconPos="right"
          [disabled]="!canProceed"
          (click)="nextStep()"
        ></button>

        <p-button
          *ngIf="isLastStep"
          type="button"
          label="Finalize Contract"
          icon="pi pi-check"
          styleClass="p-button-success"
          [disabled]="!canProceed || !form.valid"
          [loading]="loading"
          (click)="finalizeContract()"
        ></p-button>
      </div>
    </div>
  </div>
</p-dialog>
