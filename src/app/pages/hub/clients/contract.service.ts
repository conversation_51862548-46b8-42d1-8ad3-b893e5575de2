import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { Observable, map, firstValueFrom } from 'rxjs';
import { Contract, ContractStatus, Office } from './tenant.model';

@Injectable({
  providedIn: 'root',
})
export class ContractService {
  constructor(private afs: AngularFirestore, private afAuth: AngularFireAuth) {}

  // Get all available offices for a specific branch
  getAvailableOffices(branchId?: string): Observable<Office[]> {
    return new Observable<Office[]>((observer) => {
      this.afAuth.authState.subscribe((user) => {
        if (user) {
          let query = this.afs.collection('offices', (ref) => {
            let baseQuery = ref.where('status', '==', 'available');
            if (branchId) {
              baseQuery = baseQuery.where('branchId', '==', branchId);
            }
            return baseQuery;
          });

          query.valueChanges().subscribe((offices: any[]) => {
            const sortedOffices = offices
              .map((office) => ({
                ...office,
                officeId: office.officeId || office.id,
              }))
              .sort((a, b) => a.officeNumber - b.officeNumber);
            observer.next(sortedOffices);
          });
        } else {
          observer.next([]);
        }
      });
    });
  }

  // Get all offices (for office selection) - includes all statuses for admin view
  getAllOffices(branchId?: string): Observable<Office[]> {
    return new Observable<Office[]>((observer) => {
      this.afAuth.authState.subscribe((user) => {
        if (user) {
          let query = this.afs.collection('offices', (ref) => {
            if (branchId) {
              return ref.where('branchId', '==', branchId);
            }
            return ref;
          });

          query.valueChanges().subscribe((offices: any[]) => {
            const sortedOffices = offices
              .map((office) => ({
                ...office,
                officeId: office.officeId || office.id,
              }))
              .sort((a, b) => a.officeNumber - b.officeNumber);
            observer.next(sortedOffices);
          });
        } else {
          observer.next([]);
        }
      });
    });
  }

  // Get branches for the current user (similar to branches component)
  getUserBranches(): Observable<any[]> {
    return new Observable<any[]>((observer) => {
      this.afAuth.authState.subscribe((user) => {
        if (user) {
          // Check user type first
          this.afs
            .collection('users')
            .doc(user.uid)
            .get()
            .subscribe((userDoc) => {
              const userData = userDoc.data() as any;

              if (userData?.branchId) {
                // Regular user - get single branch
                this.afs
                  .collection('branches')
                  .doc(userData.branchId)
                  .get()
                  .subscribe((branch) => {
                    if (branch.exists) {
                      const branchData = branch.data();
                      if (branchData) {
                        observer.next([{ ...branchData, docid: branch.id }]);
                      }
                    }
                  });
              } else {
                // Admin/Manager - get all branches
                const query1 = this.afs
                  .collection('branches', (ref) =>
                    ref.where('uid', '==', user.uid)
                  )
                  .snapshotChanges();
                const query2 = this.afs
                  .collection('branches', (ref) =>
                    ref.where('managerId', '==', user.uid)
                  )
                  .snapshotChanges();

                query1.subscribe((changes1) => {
                  const branches1 = changes1.map((change) => ({
                    ...change.payload.doc.data(),
                    docid: change.payload.doc.id,
                  }));

                  query2.subscribe((changes2) => {
                    const branches2 = changes2.map((change) => ({
                      ...change.payload.doc.data(),
                      docid: change.payload.doc.id,
                    }));

                    // Combine and remove duplicates
                    const combinedBranches = [...branches1, ...branches2];
                    const uniqueBranches = this.removeDuplicates(
                      combinedBranches,
                      'docid'
                    );
                    observer.next(uniqueBranches);
                  });
                });
              }
            });
        } else {
          observer.next([]);
        }
      });
    });
  }

  private removeDuplicates(arr: any[], key: string) {
    const seen = new Set();
    return arr.filter((item) => {
      const k = item[key];
      return seen.has(k) ? false : seen.add(k);
    });
  }

  // Create a new draft contract (using local storage)
  async createDraftContract(
    officeId?: string,
    initialData?: Partial<Contract>
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            const contractId = this.generateLocalContractId();
            const contractNumber = this.generateLocalContractNumber();

            const contract: Contract = {
              id: contractId,
              contractNumber,
              status: ContractStatus.DRAFT,
              officeId: officeId || '', // Allow empty office ID for initial drafts
              rentalType: 'Monthly',
              currency: 'USD',
              createdAt: new Date(),
              updatedAt: new Date(),
              createdBy: user.uid,
              currentStep: 0,
              ...initialData,
            };

            // Save to local storage instead of Firestore
            this.saveDraftToLocalStorage(contractId, contract);

            // Update office status to under_processing only if office is selected
            if (officeId) {
              await this.updateOfficeStatus(officeId, 'under_processing');
            }

            resolve(contractId);
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Save draft contract data (using local storage)
  async saveDraftContract(
    contractId: string,
    data: Partial<Contract>,
    currentStep?: number
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // Get existing draft from local storage
            const existingContract = this.getDraftFromLocalStorage(contractId);
            if (!existingContract) {
              reject(new Error('Draft contract not found'));
              return;
            }

            const updateData: Partial<Contract> = {
              ...data,
              updatedAt: new Date(),
              lastModifiedBy: user.uid,
            };

            if (currentStep !== undefined) {
              updateData.currentStep = currentStep;
            }

            // Merge with existing contract data
            const updatedContract = {
              ...existingContract,
              ...updateData,
            };

            // Handle office selection changes
            const oldOfficeId = existingContract.officeId;
            const newOfficeId = updateData.officeId;

            // If office changed, update office statuses
            if (oldOfficeId !== newOfficeId) {
              // Reset old office status if it was set
              if (oldOfficeId && oldOfficeId !== '') {
                await this.updateOfficeStatus(oldOfficeId, 'available');
              }
              // Set new office status if selected
              if (newOfficeId && newOfficeId !== '') {
                await this.updateOfficeStatus(newOfficeId, 'under_processing');
              }
            }

            // Save updated contract to local storage
            this.saveDraftToLocalStorage(contractId, updatedContract);

            resolve();
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Finalize contract (change from draft to active and move to Firestore)
  async finalizeContract(contractId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // Get the draft contract from local storage
            const contract = this.getDraftFromLocalStorage(contractId);
            if (!contract) {
              reject(new Error('Draft contract not found'));
              return;
            }

            // Ensure office is selected before finalizing
            if (!contract.officeId || contract.officeId === '') {
              reject(
                new Error('Office must be selected before finalizing contract')
              );
              return;
            }

            // Generate a new contract number for the finalized contract
            const contractNumber = await this.generateContractNumber();

            // Create the final contract in Firestore
            const finalContract: Contract = {
              ...contract,
              contractNumber,
              status: ContractStatus.ACTIVE,
              updatedAt: new Date(),
              lastModifiedBy: user.uid,
            };

            // Save to Firestore
            await this.afs
              .collection('contracts')
              .doc(contractId)
              .set(finalContract);

            // Remove from local storage
            this.removeDraftFromLocalStorage(contractId);

            // Update office status to occupied
            await this.updateOfficeStatus(contract.officeId, 'occupied');

            resolve();
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Cancel draft contract (remove from local storage)
  async cancelDraftContract(contractId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            const contract = this.getDraftFromLocalStorage(contractId);
            if (!contract) {
              reject(new Error('Draft contract not found'));
              return;
            }

            // Remove from local storage
            this.removeDraftFromLocalStorage(contractId);

            // Reset office status to available only if office was selected
            if (contract.officeId && contract.officeId !== '') {
              await this.updateOfficeStatus(contract.officeId, 'available');
            }

            resolve();
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Get contract by ID (check local storage first for drafts)
  getContract(contractId: string): Observable<Contract | undefined> {
    console.log('getContract called with contractId:', contractId); // Debug log

    // For draft contracts, use synchronous localStorage access
    if (contractId.startsWith('draft_')) {
      const draftContract = this.getDraftFromLocalStorageSync(contractId);
      console.log('Draft contract found:', draftContract); // Debug log

      return new Observable<Contract | undefined>((observer) => {
        observer.next(draftContract || undefined);
        observer.complete();
      });
    }

    console.log('Not a draft, checking Firestore'); // Debug log
    // If not a draft, check Firestore
    return this.afs
      .collection('contracts')
      .doc<Contract>(contractId)
      .valueChanges({ idField: 'id' });
  }

  // Get contract with populated office and tenant data
  getContractWithDetails(contractId: string): Observable<Contract | undefined> {
    return this.getContract(contractId).pipe(
      map((contract) => {
        if (!contract) return undefined;

        // You can extend this to populate office and tenant details
        // For now, returning the contract as is
        return contract;
      })
    );
  }

  // Get all contracts
  getAllContracts(): Observable<Contract[]> {
    return this.afs
      .collection<Contract>('contracts', (ref) =>
        ref.orderBy('createdAt', 'desc')
      )
      .valueChanges({ idField: 'id' });
  }

  // Get draft contracts (from local storage)
  getDraftContracts(): Observable<Contract[]> {
    return new Observable<Contract[]>((observer) => {
      const drafts = this.getAllDraftsFromLocalStorage();
      observer.next(drafts);
      observer.complete();
    });
  }

  // Update office status
  private async updateOfficeStatus(
    officeId: string,
    status: 'available' | 'occupied' | 'under_processing' | 'maintenance'
  ): Promise<void> {
    await this.afs.collection('offices').doc(officeId).update({
      status,
      updatedAt: new Date(),
    });
  }

  // Generate unique contract number
  private async generateContractNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');

    // Get the count of contracts for this month
    const contractsSnapshot = await firstValueFrom(
      this.afs
        .collection('contracts', (ref) =>
          ref
            .where('createdAt', '>=', new Date(year, new Date().getMonth(), 1))
            .where(
              'createdAt',
              '<',
              new Date(year, new Date().getMonth() + 1, 1)
            )
        )
        .get()
    );

    const count = (contractsSnapshot?.size || 0) + 1;
    const contractNumber = `CON-${year}${month}-${String(count).padStart(
      4,
      '0'
    )}`;

    return contractNumber;
  }

  // Local storage helper methods for draft contracts
  private readonly DRAFT_CONTRACTS_KEY = 'draft_contracts';

  private generateLocalContractId(): string {
    return (
      'draft_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    );
  }

  private generateLocalContractNumber(): string {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-4);
    return `DRAFT-${year}${month}-${timestamp}`;
  }

  private saveDraftToLocalStorage(
    contractId: string,
    contract: Contract
  ): void {
    try {
      console.log('Saving draft to localStorage:', contractId, contract); // Debug log
      const existingDrafts = this.getAllDraftsFromLocalStorage();
      const draftsMap = new Map(existingDrafts.map((d) => [d.id!, d]));
      draftsMap.set(contractId, contract);

      const draftsArray = Array.from(draftsMap.values());
      localStorage.setItem(
        this.DRAFT_CONTRACTS_KEY,
        JSON.stringify(draftsArray)
      );
      console.log('Draft saved successfully to localStorage'); // Debug log
    } catch (error) {
      console.error('Error saving draft to local storage:', error);
    }
  }

  getDraftFromLocalStorage(contractId: string): Contract | null {
    return this.getDraftFromLocalStorageSync(contractId);
  }

  // Synchronous method to get draft from localStorage without date processing issues
  private getDraftFromLocalStorageSync(contractId: string): Contract | null {
    try {
      const draftsJson = localStorage.getItem(this.DRAFT_CONTRACTS_KEY);
      console.log('getDraftFromLocalStorageSync - raw JSON:', draftsJson); // Debug log

      if (!draftsJson) {
        console.log('No drafts found in localStorage');
        return null;
      }

      const drafts = JSON.parse(draftsJson) as Contract[];
      console.log('getDraftFromLocalStorageSync - parsed drafts:', drafts); // Debug log

      const foundDraft =
        drafts.find((draft) => draft.id === contractId) || null;
      console.log(
        'getDraftFromLocalStorageSync - found draft:',
        contractId,
        foundDraft
      ); // Debug log

      if (foundDraft) {
        // Convert date strings back to Date objects
        return {
          ...foundDraft,
          createdAt: foundDraft.createdAt
            ? new Date(foundDraft.createdAt)
            : new Date(),
          updatedAt: foundDraft.updatedAt
            ? new Date(foundDraft.updatedAt)
            : new Date(),
          startDate: foundDraft.startDate
            ? new Date(foundDraft.startDate)
            : undefined,
          endDate: foundDraft.endDate
            ? new Date(foundDraft.endDate)
            : undefined,
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting draft from local storage sync:', error);
      return null;
    }
  }

  private getAllDraftsFromLocalStorage(): Contract[] {
    try {
      const draftsJson = localStorage.getItem(this.DRAFT_CONTRACTS_KEY);
      console.log('getAllDraftsFromLocalStorage - raw JSON:', draftsJson); // Debug log
      if (!draftsJson) return [];

      const drafts = JSON.parse(draftsJson) as Contract[];
      console.log('getAllDraftsFromLocalStorage - parsed drafts:', drafts); // Debug log

      // Convert date strings back to Date objects
      const processedDrafts = drafts
        .map((draft) => ({
          ...draft,
          createdAt: draft.createdAt ? new Date(draft.createdAt) : new Date(),
          updatedAt: draft.updatedAt ? new Date(draft.updatedAt) : new Date(),
          startDate: draft.startDate ? new Date(draft.startDate) : undefined,
          endDate: draft.endDate ? new Date(draft.endDate) : undefined,
        }))
        .sort(
          (a, b) =>
            (b.updatedAt?.getTime() || 0) - (a.updatedAt?.getTime() || 0)
        );

      console.log(
        'getAllDraftsFromLocalStorage - processed drafts:',
        processedDrafts
      ); // Debug log
      return processedDrafts;
    } catch (error) {
      console.error('Error getting all drafts from local storage:', error);
      return [];
    }
  }

  private removeDraftFromLocalStorage(contractId: string): void {
    try {
      const drafts = this.getAllDraftsFromLocalStorage();
      const filteredDrafts = drafts.filter((draft) => draft.id !== contractId);
      localStorage.setItem(
        this.DRAFT_CONTRACTS_KEY,
        JSON.stringify(filteredDrafts)
      );
    } catch (error) {
      console.error('Error removing draft from local storage:', error);
    }
  }

  // Clear all draft contracts from local storage (utility method)
  clearAllDraftsFromLocalStorage(): void {
    try {
      localStorage.removeItem(this.DRAFT_CONTRACTS_KEY);
    } catch (error) {
      console.error('Error clearing drafts from local storage:', error);
    }
  }

  // Debug method to inspect localStorage data
  debugLocalStorageData(): void {
    try {
      const draftsJson = localStorage.getItem(this.DRAFT_CONTRACTS_KEY);
      console.log('Raw localStorage data:', draftsJson);
      if (draftsJson) {
        const drafts = JSON.parse(draftsJson);
        console.log('Parsed drafts:', drafts);
      }
    } catch (error) {
      console.error('Error debugging localStorage:', error);
    }
  }
}
